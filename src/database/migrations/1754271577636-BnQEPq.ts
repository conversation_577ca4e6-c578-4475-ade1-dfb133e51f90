import typeorm = require('typeorm');

class BnQEPq1754271577636 implements typeorm.MigrationInterface {
    name = 'BnQEPq1754271577636'

    public async up(queryRunner: typeorm.QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`user_captcha\` (\`id\` varchar(36) NOT NULL, \`code\` varchar(255) NOT NULL COMMENT '验证码', \`action\` enum ('login', 'register', 'retrieve-password', 'reset-password', 'account-bound') NOT NULL COMMENT '验证操作类型', \`type\` enum ('phone', 'email') NOT NULL COMMENT '验证码类型', \`value\` varchar(255) NOT NULL COMMENT '手机号/邮箱地址', \`createdAt\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: typeorm.QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE \`user_captcha\``);
    }

}

module.exports = BnQEPq1754271577636
