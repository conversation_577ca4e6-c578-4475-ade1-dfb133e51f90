import { Exclude, Expose, Type } from 'class-transformer';
import type { Relation } from 'typeorm';
import {
    BaseEntity,
    Column,
    Entity,
    JoinTable,
    ManyToMany,
    PrimaryGeneratedColumn,
    Tree,
    TreeChildren,
    TreeParent,
} from 'typeorm';

import { RoleEntity } from './role.entity';

/**
 * 菜单类型枚举
 */
export enum MenuType {
    MENU = 'menu',       // 菜单
    BUTTON = 'button',   // 按钮
    LINK = 'link',       // 外链
}

/**
 * 菜单实体
 */
@Exclude()
@Entity('rbac_menu')
@Tree('materialized-path')
export class MenuEntity extends BaseEntity {
    /**
     * 菜单ID
     */
    @Expose()
    @PrimaryGeneratedColumn('uuid')
    id: string;

    /**
     * 菜单名称
     */
    @Expose()
    @Column({ comment: '菜单名称' })
    name: string;

    /**
     * 菜单标题（显示名称）
     */
    @Expose()
    @Column({ comment: '菜单标题', nullable: true })
    title?: string;

    /**
     * 菜单图标
     */
    @Expose()
    @Column({ comment: '菜单图标', nullable: true })
    icon?: string;

    /**
     * 菜单路径/路由
     */
    @Expose()
    @Column({ comment: '菜单路径', nullable: true })
    path?: string;

    /**
     * 组件路径
     */
    @Expose()
    @Column({ comment: '组件路径', nullable: true })
    component?: string;

    /**
     * 菜单类型
     */
    @Expose()
    @Column({
        type: 'enum',
        enum: MenuType,
        default: MenuType.MENU,
        comment: '菜单类型',
    })
    type: MenuType;

    /**
     * 权限标识
     */
    @Expose()
    @Column({ comment: '权限标识', nullable: true })
    permission?: string;

    /**
     * 排序
     */
    @Expose()
    @Column({ comment: '排序', default: 0 })
    sort: number;

    /**
     * 是否隐藏
     */
    @Expose()
    @Column({ comment: '是否隐藏', default: false })
    hidden: boolean;

    /**
     * 是否缓存
     */
    @Expose()
    @Column({ comment: '是否缓存', default: false })
    keepAlive: boolean;

    /**
     * 外链地址
     */
    @Expose()
    @Column({ comment: '外链地址', nullable: true })
    externalLink?: string;

    /**
     * 菜单描述
     */
    @Expose()
    @Column({ comment: '菜单描述', nullable: true, type: 'text' })
    description?: string;

    /**
     * 是否启用
     */
    @Expose()
    @Column({ comment: '是否启用', default: true })
    enabled: boolean;

    /**
     * 父级菜单
     */
    @Expose({ groups: ['menu-detail', 'menu-tree'] })
    @TreeParent({ onDelete: 'CASCADE' })
    parent: Relation<MenuEntity> | null;

    /**
     * 子级菜单
     */
    @Expose({ groups: ['menu-tree'] })
    @Type(() => MenuEntity)
    @TreeChildren({ cascade: true })
    children: Relation<MenuEntity>[];

    /**
     * 菜单关联的角色
     */
    @Expose({ groups: ['menu-detail'] })
    @ManyToMany(() => RoleEntity, (role) => role.menus)
    @JoinTable({
        name: 'rbac_role_menu',
        joinColumn: { name: 'menu_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
    })
    roles: Relation<RoleEntity>[];

    /**
     * 菜单层级深度
     */
    @Expose({ groups: ['menu-list'] })
    depth = 0;
}
