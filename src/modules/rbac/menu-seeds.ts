import { MenuType } from './entities/menu.entity';

/**
 * 默认菜单数据
 */
export const defaultMenus = [
    // 系统管理
    {
        name: 'system',
        title: '系统管理',
        icon: 'SettingOutlined',
        path: '/system',
        type: MenuType.MENU,
        sort: 1000,
        permission: 'system.manage',
        children: [
            {
                name: 'system-user',
                title: '用户管理',
                icon: 'UserOutlined',
                path: '/system/user',
                component: 'system/user/index',
                type: MenuType.MENU,
                sort: 1,
                permission: 'user.manage',
            },
            {
                name: 'system-role',
                title: '角色管理',
                icon: 'TeamOutlined',
                path: '/system/role',
                component: 'system/role/index',
                type: MenuType.MENU,
                sort: 2,
                permission: 'role.manage',
            },
            {
                name: 'system-permission',
                title: '权限管理',
                icon: 'SafetyOutlined',
                path: '/system/permission',
                component: 'system/permission/index',
                type: MenuType.MENU,
                sort: 3,
                permission: 'permission.manage',
            },
            {
                name: 'system-menu',
                title: '菜单管理',
                icon: 'MenuOutlined',
                path: '/system/menu',
                component: 'system/menu/index',
                type: MenuType.MENU,
                sort: 4,
                permission: 'menu.manage',
            },
        ],
    },
    // 内容管理
    {
        name: 'content',
        title: '内容管理',
        icon: 'FileTextOutlined',
        path: '/content',
        type: MenuType.MENU,
        sort: 2000,
        permission: 'content.manage',
        children: [
            {
                name: 'content-category',
                title: '分类管理',
                icon: 'FolderOutlined',
                path: '/content/category',
                component: 'content/category/index',
                type: MenuType.MENU,
                sort: 1,
                permission: 'category.manage',
            },
            {
                name: 'content-post',
                title: '文章管理',
                icon: 'EditOutlined',
                path: '/content/post',
                component: 'content/post/index',
                type: MenuType.MENU,
                sort: 2,
                permission: 'post.manage',
            },
            {
                name: 'content-comment',
                title: '评论管理',
                icon: 'CommentOutlined',
                path: '/content/comment',
                component: 'content/comment/index',
                type: MenuType.MENU,
                sort: 3,
                permission: 'comment.manage',
            },
        ],
    },
    // 个人中心
    {
        name: 'profile',
        title: '个人中心',
        icon: 'UserOutlined',
        path: '/profile',
        type: MenuType.MENU,
        sort: 9000,
        children: [
            {
                name: 'profile-info',
                title: '个人信息',
                icon: 'IdcardOutlined',
                path: '/profile/info',
                component: 'profile/info/index',
                type: MenuType.MENU,
                sort: 1,
            },
            {
                name: 'profile-password',
                title: '修改密码',
                icon: 'LockOutlined',
                path: '/profile/password',
                component: 'profile/password/index',
                type: MenuType.MENU,
                sort: 2,
            },
        ],
    },
];

/**
 * 按钮权限数据
 */
export const defaultButtons = [
    // 用户管理按钮
    {
        name: 'user-create',
        title: '新增用户',
        type: MenuType.BUTTON,
        permission: 'user.create',
        parent: 'system-user',
    },
    {
        name: 'user-update',
        title: '编辑用户',
        type: MenuType.BUTTON,
        permission: 'user.update',
        parent: 'system-user',
    },
    {
        name: 'user-delete',
        title: '删除用户',
        type: MenuType.BUTTON,
        permission: 'user.delete',
        parent: 'system-user',
    },
    // 角色管理按钮
    {
        name: 'role-create',
        title: '新增角色',
        type: MenuType.BUTTON,
        permission: 'role.create',
        parent: 'system-role',
    },
    {
        name: 'role-update',
        title: '编辑角色',
        type: MenuType.BUTTON,
        permission: 'role.update',
        parent: 'system-role',
    },
    {
        name: 'role-delete',
        title: '删除角色',
        type: MenuType.BUTTON,
        permission: 'role.delete',
        parent: 'system-role',
    },
    // 菜单管理按钮
    {
        name: 'menu-create',
        title: '新增菜单',
        type: MenuType.BUTTON,
        permission: 'menu.create',
        parent: 'system-menu',
    },
    {
        name: 'menu-update',
        title: '编辑菜单',
        type: MenuType.BUTTON,
        permission: 'menu.update',
        parent: 'system-menu',
    },
    {
        name: 'menu-delete',
        title: '删除菜单',
        type: MenuType.BUTTON,
        permission: 'menu.delete',
        parent: 'system-menu',
    },
    // 文章管理按钮
    {
        name: 'post-create',
        title: '新增文章',
        type: MenuType.BUTTON,
        permission: 'post.create',
        parent: 'content-post',
    },
    {
        name: 'post-update',
        title: '编辑文章',
        type: MenuType.BUTTON,
        permission: 'post.update',
        parent: 'content-post',
    },
    {
        name: 'post-delete',
        title: '删除文章',
        type: MenuType.BUTTON,
        permission: 'post.delete',
        parent: 'content-post',
    },
];
