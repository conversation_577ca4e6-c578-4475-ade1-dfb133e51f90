import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseUUIDPipe,
    Patch,
    Post,
    Put,
    Query,
    SerializeOptions,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';

import { PermissionAction } from '@/modules/rbac/constants';
import { Permission } from '@/modules/rbac/decorators/permission.decorator';
import { MenuEntity } from '@/modules/rbac/entities';
import { RbacModule } from '@/modules/rbac/rbac.module';
import { CreateMenuDto, MenuService, QueryMenuDto, UpdateMenuDto } from '@/modules/rbac/services/menu.service';
import { PermissionChecker } from '@/modules/rbac/types';
import { Depends } from '@/modules/restful/decorators/depend.decorator';

// 菜单管理权限检查器
const menuPermissions: Record<'manage' | 'create' | 'update' | 'delete', PermissionChecker> = {
    manage: async (ab) => ab.can(PermissionAction.MANAGE, MenuEntity.name),
    create: async (ab) => ab.can(PermissionAction.CREATE, MenuEntity.name),
    update: async (ab) => ab.can(PermissionAction.UPDATE, MenuEntity.name),
    delete: async (ab) => ab.can(PermissionAction.DELETE, MenuEntity.name),
};

@ApiTags('菜单管理')
@ApiBearerAuth()
@Depends(RbacModule)
@Controller('menus')
export class MenuController {
    constructor(private service: MenuService) {}

    /**
     * 查询菜单树
     */
    @Get('tree')
    @ApiOperation({ summary: '查询菜单树' })
    @SerializeOptions({ groups: ['menu-tree'] })
    @Permission(menuPermissions.manage)
    async tree(@Query() options: QueryMenuDto) {
        return this.service.findTrees(options);
    }

    /**
     * 查询菜单列表
     */
    @Get()
    @ApiOperation({ summary: '查询菜单列表' })
    @SerializeOptions({ groups: ['menu-list'] })
    @Permission(menuPermissions.manage)
    async list(@Query() options: QueryMenuDto) {
        return this.service.findTrees(options);
    }

    /**
     * 查询菜单详情
     */
    @Get(':id')
    @ApiOperation({ summary: '查询菜单详情' })
    @SerializeOptions({ groups: ['menu-detail'] })
    @Permission(menuPermissions.manage)
    async detail(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.service.detail(id);
    }

    /**
     * 创建菜单
     */
    @Post()
    @ApiOperation({ summary: '创建菜单' })
    @SerializeOptions({ groups: ['menu-detail'] })
    @Permission(menuPermissions.create)
    async create(@Body() data: CreateMenuDto) {
        return this.service.create(data);
    }

    /**
     * 更新菜单
     */
    @Put(':id')
    @ApiOperation({ summary: '更新菜单' })
    @SerializeOptions({ groups: ['menu-detail'] })
    @Permission(menuPermissions.update)
    async update(
        @Param('id', new ParseUUIDPipe()) id: string,
        @Body() data: Omit<UpdateMenuDto, 'id'>,
    ) {
        return this.service.update({ ...data, id });
    }

    /**
     * 删除菜单
     */
    @Delete(':id')
    @ApiOperation({ summary: '删除菜单' })
    @Permission(menuPermissions.delete)
    async delete(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.service.delete(id);
    }

    /**
     * 批量删除菜单
     */
    @Delete()
    @ApiOperation({ summary: '批量删除菜单' })
    @Permission(menuPermissions.delete)
    async deleteMultiple(@Body() { ids }: { ids: string[] }) {
        return this.service.deleteMultiple(ids);
    }

    /**
     * 移动菜单
     */
    @Patch(':id/move')
    @ApiOperation({ summary: '移动菜单' })
    @SerializeOptions({ groups: ['menu-detail'] })
    @Permission(menuPermissions.update)
    async move(
        @Param('id', new ParseUUIDPipe()) id: string,
        @Body() { parentId }: { parentId?: string },
    ) {
        return this.service.move(id, parentId);
    }

    /**
     * 更新菜单排序
     */
    @Patch(':id/sort')
    @ApiOperation({ summary: '更新菜单排序' })
    @SerializeOptions({ groups: ['menu-detail'] })
    @Permission(menuPermissions.update)
    async updateSort(
        @Param('id', new ParseUUIDPipe()) id: string,
        @Body() { sort }: { sort: number },
    ) {
        return this.service.updateSort(id, sort);
    }

    /**
     * 切换菜单启用状态
     */
    @Patch(':id/toggle-enabled')
    @ApiOperation({ summary: '切换菜单启用状态' })
    @SerializeOptions({ groups: ['menu-detail'] })
    @Permission(menuPermissions.update)
    async toggleEnabled(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.service.toggleEnabled(id);
    }

    /**
     * 切换菜单隐藏状态
     */
    @Patch(':id/toggle-hidden')
    @ApiOperation({ summary: '切换菜单隐藏状态' })
    @SerializeOptions({ groups: ['menu-detail'] })
    @Permission(menuPermissions.update)
    async toggleHidden(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.service.toggleHidden(id);
    }
}
