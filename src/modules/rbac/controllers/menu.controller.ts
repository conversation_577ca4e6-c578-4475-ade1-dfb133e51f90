import { Controller, Get, SerializeOptions } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';

import { RequestUser } from '@/modules/user/decorators/user.request.decorator';
import { UserEntity } from '@/modules/user/entities';

import { RbacModule } from '../rbac.module';
import { MenuService } from '../services/menu.service';
import { Depends } from '@/modules/restful/decorators/depend.decorator';

@ApiTags('用户菜单')
@ApiBearerAuth()
@Depends(RbacModule)
@Controller('user-menus')
export class UserMenuController {
    constructor(private service: MenuService) {}

    /**
     * 获取当前用户的菜单树
     */
    @Get('tree')
    @ApiOperation({ summary: '获取当前用户的菜单树' })
    @SerializeOptions({ groups: ['menu-tree'] })
    async getUserMenuTree(@RequestUser() user: UserEntity) {
        const roleIds = user.roles?.map(role => role.id) || [];
        return this.service.getUserMenuTree(roleIds);
    }

    /**
     * 获取当前用户的菜单权限列表
     */
    @Get('permissions')
    @ApiOperation({ summary: '获取当前用户的菜单权限列表' })
    async getUserMenuPermissions(@RequestUser() user: UserEntity) {
        const roleIds = user.roles?.map(role => role.id) || [];
        const permissions = await this.service.getUserMenuPermissions(roleIds);
        return { permissions };
    }
}
