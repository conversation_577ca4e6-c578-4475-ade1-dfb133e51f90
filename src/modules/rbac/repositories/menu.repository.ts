import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';

import { BaseTreeRepository } from '@/modules/database/base/tree.repository';

import { MenuEntity } from '../entities/menu.entity';

@Injectable()
export class MenuRepository extends BaseTreeRepository<MenuEntity> {
    protected _qbName = 'menu';

    protected orderBy = { name: 'menu.sort', order: 'ASC' } as const;

    constructor(protected dataSource: DataSource) {
        super(MenuEntity, dataSource.createEntityManager());
    }

    /**
     * 构建基础查询器
     */
    buildBaseQuery() {
        return this.createQueryBuilder(this.qbName)
            .leftJoinAndSelect(`${this.qbName}.parent`, 'parent')
            .leftJoinAndSelect(`${this.qbName}.children`, 'children')
            .orderBy('menu.sort', 'ASC');
    }

    /**
     * 根据角色ID查询菜单
     */
    async findByRoles(roleIds: string[]) {
        return this.createQueryBuilder(this.qbName)
            .leftJoinAndSelect(`${this.qbName}.roles`, 'roles')
            .where('roles.id IN (:...roleIds)', { roleIds })
            .andWhere(`${this.qbName}.enabled = :enabled`, { enabled: true })
            .orderBy('menu.sort', 'ASC')
            .getMany();
    }

    /**
     * 查询用户可访问的菜单树
     */
    async findUserMenuTree(roleIds: string[]) {
        const menus = await this.findByRoles(roleIds);
        return this.buildMenuTree(menus);
    }

    /**
     * 构建菜单树结构
     */
    private buildMenuTree(menus: MenuEntity[]): any[] {
        const menuMap = new Map<string, any>();
        const rootMenus: any[] = [];

        // 创建菜单映射
        menus.forEach(menu => {
            menuMap.set(menu.id, {
                ...menu,
                children: [],
                parent: menu.parent ? { id: menu.parent.id } : null
            });
        });

        // 构建树结构
        menus.forEach(menu => {
            const menuItem = menuMap.get(menu.id)!;
            if (menu.parent) {
                const parent = menuMap.get(menu.parent.id);
                if (parent) {
                    parent.children = parent.children || [];
                    parent.children.push(menuItem);
                }
            } else {
                rootMenus.push(menuItem);
            }
        });

        return rootMenus;
    }
}
