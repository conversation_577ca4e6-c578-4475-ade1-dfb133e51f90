# 菜单权限系统使用指南

## 🎯 系统概述

本菜单权限系统基于 RBAC (Role-Based Access Control) 模型，将页面菜单与权限系统深度集成，实现细粒度的前端权限控制。

## 📋 核心特性

### 1. 菜单类型
- **MENU**: 页面菜单，对应前端路由
- **BUTTON**: 按钮权限，控制页面内操作按钮
- **LINK**: 外部链接

### 2. 权限控制
- **角色菜单关联**: 角色可以关联多个菜单
- **权限标识**: 每个菜单可以设置权限标识
- **层级控制**: 支持菜单树形结构
- **动态加载**: 根据用户角色动态生成菜单

## 🚀 API 接口

### 管理端接口 (需要权限)

```typescript
// 菜单管理
GET    /api/manage/rbac/menus/tree        // 查询菜单树
GET    /api/manage/rbac/menus             // 查询菜单列表
GET    /api/manage/rbac/menus/:id         // 查询菜单详情
POST   /api/manage/rbac/menus             // 创建菜单
PUT    /api/manage/rbac/menus/:id         // 更新菜单
DELETE /api/manage/rbac/menus/:id         // 删除菜单
DELETE /api/manage/rbac/menus             // 批量删除菜单

// 菜单操作
PATCH  /api/manage/rbac/menus/:id/move           // 移动菜单
PATCH  /api/manage/rbac/menus/:id/sort           // 更新排序
PATCH  /api/manage/rbac/menus/:id/toggle-enabled // 切换启用状态
PATCH  /api/manage/rbac/menus/:id/toggle-hidden  // 切换隐藏状态
```

### 用户端接口 (需要登录)

```typescript
// 用户菜单
GET /api/app/rbac/user-menus/tree         // 获取当前用户菜单树
GET /api/app/rbac/user-menus/permissions  // 获取当前用户菜单权限列表
```

## 💻 前端集成示例

### 1. 获取用户菜单

```typescript
// 获取用户菜单树
const getUserMenus = async () => {
  const response = await fetch('/api/app/rbac/user-menus/tree', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// 获取用户权限列表
const getUserPermissions = async () => {
  const response = await fetch('/api/app/rbac/user-menus/permissions', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};
```

### 2. React 路由集成

```typescript
// 动态路由生成
import { Route, Routes } from 'react-router-dom';

const DynamicRoutes = ({ menus }) => {
  const generateRoutes = (menuList) => {
    return menuList.map(menu => {
      if (menu.type === 'menu' && menu.component) {
        return (
          <Route
            key={menu.id}
            path={menu.path}
            element={<LazyComponent component={menu.component} />}
          >
            {menu.children && generateRoutes(menu.children)}
          </Route>
        );
      }
      return null;
    }).filter(Boolean);
  };

  return (
    <Routes>
      {generateRoutes(menus)}
    </Routes>
  );
};
```

### 3. 菜单组件示例

```typescript
// 侧边栏菜单组件
import { Menu } from 'antd';
import { useNavigate } from 'react-router-dom';

const SideMenu = ({ menus }) => {
  const navigate = useNavigate();

  const generateMenuItems = (menuList) => {
    return menuList
      .filter(menu => !menu.hidden && menu.enabled)
      .map(menu => ({
        key: menu.id,
        icon: menu.icon ? <Icon component={menu.icon} /> : null,
        label: menu.title,
        children: menu.children ? generateMenuItems(menu.children) : undefined,
        onClick: menu.path ? () => navigate(menu.path) : undefined,
      }));
  };

  return (
    <Menu
      mode="inline"
      items={generateMenuItems(menus)}
    />
  );
};
```

### 4. 权限控制组件

```typescript
// 权限控制高阶组件
const withPermission = (permission: string) => (Component) => {
  return (props) => {
    const { permissions } = useAuth(); // 从上下文获取权限列表
    
    if (!permissions.includes(permission)) {
      return null; // 或者返回无权限提示组件
    }
    
    return <Component {...props} />;
  };
};

// 使用示例
const CreateButton = withPermission('user.create')(() => (
  <Button type="primary">新增用户</Button>
));

// 或者使用 Hook
const usePermission = (permission: string) => {
  const { permissions } = useAuth();
  return permissions.includes(permission);
};

const UserManagement = () => {
  const canCreate = usePermission('user.create');
  const canUpdate = usePermission('user.update');
  const canDelete = usePermission('user.delete');

  return (
    <div>
      {canCreate && <Button>新增</Button>}
      {canUpdate && <Button>编辑</Button>}
      {canDelete && <Button>删除</Button>}
    </div>
  );
};
```

## 🔧 后端配置

### 1. 菜单数据初始化

```typescript
// 在应用启动时初始化菜单数据
import { MenuService } from '@/modules/rbac/services/menu.service';
import { defaultMenus, defaultButtons } from '@/modules/rbac/menu-seeds';

@Injectable()
export class MenuInitService implements OnApplicationBootstrap {
  constructor(private menuService: MenuService) {}

  async onApplicationBootstrap() {
    await this.initializeMenus();
  }

  private async initializeMenus() {
    // 初始化菜单数据
    for (const menuData of defaultMenus) {
      await this.createMenuRecursively(menuData);
    }
    
    // 初始化按钮权限
    for (const buttonData of defaultButtons) {
      await this.menuService.create(buttonData);
    }
  }

  private async createMenuRecursively(menuData: any, parentId?: string) {
    const { children, ...menu } = menuData;
    const createdMenu = await this.menuService.create({
      ...menu,
      parent: parentId,
    });

    if (children) {
      for (const child of children) {
        await this.createMenuRecursively(child, createdMenu.id);
      }
    }
  }
}
```

### 2. 角色菜单关联

```typescript
// 为角色分配菜单
const assignMenusToRole = async (roleId: string, menuIds: string[]) => {
  const role = await roleRepository.findOne({
    where: { id: roleId },
    relations: ['menus'],
  });

  const menus = await menuRepository.findByIds(menuIds);
  role.menus = menus;
  
  await roleRepository.save(role);
};
```

## 🎨 前端状态管理

### 1. Redux/Zustand 状态管理

```typescript
// 菜单状态管理
interface MenuState {
  menus: MenuEntity[];
  permissions: string[];
  loading: boolean;
}

const useMenuStore = create<MenuState>((set, get) => ({
  menus: [],
  permissions: [],
  loading: false,

  // 加载用户菜单
  loadUserMenus: async () => {
    set({ loading: true });
    try {
      const [menusRes, permissionsRes] = await Promise.all([
        getUserMenus(),
        getUserPermissions(),
      ]);
      
      set({
        menus: menusRes,
        permissions: permissionsRes.permissions,
        loading: false,
      });
    } catch (error) {
      set({ loading: false });
    }
  },

  // 检查权限
  hasPermission: (permission: string) => {
    const { permissions } = get();
    return permissions.includes(permission);
  },
}));
```

## 🔒 安全注意事项

1. **前端权限仅用于 UI 控制**：真正的权限验证必须在后端进行
2. **敏感操作双重验证**：重要操作需要后端再次验证权限
3. **权限缓存策略**：合理设置权限信息的缓存时间
4. **路由守卫**：在路由层面进行权限检查

## 📝 最佳实践

1. **权限粒度**：根据业务需求合理设计权限粒度
2. **菜单层级**：避免菜单层级过深，建议不超过3级
3. **权限命名**：使用统一的权限命名规范，如 `模块.操作`
4. **缓存策略**：合理使用缓存提升用户体验
5. **错误处理**：提供友好的无权限提示页面

这个菜单权限系统提供了完整的前后端权限控制方案，可以根据具体业务需求进行定制和扩展。
