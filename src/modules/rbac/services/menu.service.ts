import { Injectable } from '@nestjs/common';
import { isArray, isNil } from 'lodash';

import { BaseService } from '@/modules/database/base/service';

import { MenuEntity, MenuType } from '../entities/menu.entity';
import { MenuRepository } from '../repositories/menu.repository';

export interface CreateMenuDto {
    name: string;
    title?: string;
    icon?: string;
    path?: string;
    component?: string;
    type?: MenuType;
    permission?: string;
    sort?: number;
    hidden?: boolean;
    keepAlive?: boolean;
    externalLink?: string;
    description?: string;
    enabled?: boolean;
    parent?: string;
    roles?: string[];
}

export interface UpdateMenuDto extends Partial<CreateMenuDto> {
    id: string;
}

export interface QueryMenuDto {
    type?: MenuType;
    enabled?: boolean;
    hidden?: boolean;
    parent?: string;
}

@Injectable()
export class MenuService extends BaseService<MenuEntity, MenuRepository> {
    constructor(protected repository: MenuRepository) {
        super(repository);
    }

    /**
     * 查询菜单树
     */
    async findTrees(options?: QueryMenuDto) {
        const query = this.repository.buildBaseQuery();
        
        if (options?.type) {
            query.andWhere('menu.type = :type', { type: options.type });
        }
        
        if (typeof options?.enabled === 'boolean') {
            query.andWhere('menu.enabled = :enabled', { enabled: options.enabled });
        }
        
        if (typeof options?.hidden === 'boolean') {
            query.andWhere('menu.hidden = :hidden', { hidden: options.hidden });
        }

        return this.repository.findTrees();
    }

    /**
     * 根据用户角色获取菜单树
     */
    async getUserMenuTree(roleIds: string[]) {
        if (!isArray(roleIds) || roleIds.length === 0) {
            return [];
        }
        
        return this.repository.findUserMenuTree(roleIds);
    }

    /**
     * 获取用户可访问的菜单权限列表
     */
    async getUserMenuPermissions(roleIds: string[]) {
        const menus = await this.repository.findByRoles(roleIds);
        return menus
            .filter(menu => menu.permission)
            .map(menu => menu.permission)
            .filter(Boolean);
    }

    /**
     * 创建菜单
     */
    async create(data: CreateMenuDto): Promise<MenuEntity> {
        const { parent, roles, ...menuData } = data;
        
        const menu = await this.repository.save({
            ...menuData,
            parent: parent ? await this.repository.findOneOrFail({ where: { id: parent } }) : null,
        });

        if (isArray(roles) && roles.length > 0) {
            await this.repository
                .createQueryBuilder()
                .relation(MenuEntity, 'roles')
                .of(menu)
                .add(roles);
        }

        return this.detail(menu.id);
    }

    /**
     * 更新菜单
     */
    async update(data: UpdateMenuDto): Promise<MenuEntity> {
        const { id, parent, roles, ...menuData } = data;
        
        await this.repository.update(id, {
            ...menuData,
            parent: parent ? await this.repository.findOneOrFail({ where: { id: parent } }) : null,
        });

        const menu = await this.detail(id);

        if (isArray(roles)) {
            await this.repository
                .createQueryBuilder()
                .relation(MenuEntity, 'roles')
                .of(menu)
                .addAndRemove(roles, menu.roles?.map(role => role.id) || []);
        }

        return this.detail(id);
    }

    /**
     * 获取菜单详情
     */
    async detail(id: string): Promise<MenuEntity> {
        return this.repository.findOneOrFail({
            where: { id },
            relations: ['parent', 'children', 'roles'],
        });
    }

    /**
     * 删除菜单
     */
    async delete(id: string): Promise<MenuEntity> {
        const menu = await this.detail(id);
        
        // 检查是否有子菜单
        if (menu.children && menu.children.length > 0) {
            throw new Error('Cannot delete menu with children');
        }

        await this.repository.remove(menu);
        return menu;
    }

    /**
     * 批量删除菜单
     */
    async deleteMultiple(ids: string[]): Promise<MenuEntity[]> {
        const menus = await this.repository.findByIds(ids, {
            relations: ['children'],
        });

        // 检查是否有子菜单
        const menusWithChildren = menus.filter(menu => menu.children && menu.children.length > 0);
        if (menusWithChildren.length > 0) {
            throw new Error('Cannot delete menus with children');
        }

        await this.repository.remove(menus);
        return menus;
    }

    /**
     * 移动菜单
     */
    async move(id: string, parentId?: string): Promise<MenuEntity> {
        const menu = await this.detail(id);
        const parent = parentId ? await this.repository.findOneOrFail({ where: { id: parentId } }) : null;
        
        menu.parent = parent;
        await this.repository.save(menu);
        
        return this.detail(id);
    }

    /**
     * 更新菜单排序
     */
    async updateSort(id: string, sort: number): Promise<MenuEntity> {
        await this.repository.update(id, { sort });
        return this.detail(id);
    }

    /**
     * 切换菜单启用状态
     */
    async toggleEnabled(id: string): Promise<MenuEntity> {
        const menu = await this.detail(id);
        menu.enabled = !menu.enabled;
        await this.repository.save(menu);
        return menu;
    }

    /**
     * 切换菜单隐藏状态
     */
    async toggleHidden(id: string): Promise<MenuEntity> {
        const menu = await this.detail(id);
        menu.hidden = !menu.hidden;
        await this.repository.save(menu);
        return menu;
    }
}
