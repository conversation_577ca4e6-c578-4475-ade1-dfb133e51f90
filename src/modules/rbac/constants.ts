export enum SystemRoles {
    USER = 'user',
    SUPER_ADMIN = 'super_admin',
}

export const SYSTEM_PERMISSION = 'system-manage';

export const PERMISSION_CHECKERS = 'permission_checkers';

export enum PermissionAction {
    CREATE = 'create',
    READ = 'read',
    UPDATE = 'update',
    DELETE = 'delete',
    MANAGE = 'manage',
    OWNER = 'owner',
}

/**
 * 菜单权限常量
 */
export const MENU_PERMISSIONS = {
    MENU_MANAGE: 'menu.manage',
    MENU_CREATE: 'menu.create',
    MENU_UPDATE: 'menu.update',
    MENU_DELETE: 'menu.delete',
    MENU_READ: 'menu.read',
} as const;
